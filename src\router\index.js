import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: Layout,
      redirect: '/supplier',
      children: [
        {
          path: '/supplier',
          name: 'Supplier',
          component: () => import('@/views/supplier/index.vue'),
          meta: { title: '供应商档案管理' }
        },
        {
          path: '/supplier/detail/:id',
          name: 'SupplierDetail',
          component: () => import('@/views/supplier/detail.vue'),
          meta: { title: '供应商详情' }
        }
      ]
    }
  ]
})

export default router
