import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      component: Layout,
      redirect: '/supplier',
      children: [
        {
          path: '/supplier',
          name: 'Supplier',
          component: () => import('@/views/supplier/index.vue'),
          meta: { title: '供应商档案管理' }
        },
        {
          path: '/supplier/detail/:id',
          name: 'SupplierDetail',
          component: () => import('@/views/supplier/detail.vue'),
          meta: { title: '供应商详情' }
        },
        {
          path: '/supplier-development',
          name: 'SupplierDevelopment',
          component: () => import('@/views/supplier-development/index.vue'),
          meta: { title: '供应商开发与准入管理' }
        },
        {
          path: '/supplier-development/application/:id',
          name: 'SupplierApplication',
          component: () => import('@/views/supplier-development/application.vue'),
          meta: { title: '准入申请详情' }
        },
        {
          path: '/supplier-development/audit/:id',
          name: 'SupplierAudit',
          component: () => import('@/views/supplier-development/audit.vue'),
          meta: { title: '现场审核' }
        }
      ]
    }
  ]
})

export default router
