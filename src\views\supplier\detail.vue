<template>
  <div class="supplier-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="$router.go(-1)" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>{{ supplierInfo.name }} - 详细信息</h2>
    </div>

    <!-- 基础信息卡片 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>基础信息</span>
          <el-button type="primary" size="small">编辑</el-button>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>供应商编码：</label>
            <span>{{ supplierInfo.code }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>供应商名称：</label>
            <span>{{ supplierInfo.name }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>注册资本：</label>
            <span>{{ supplierInfo.registeredCapital }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>法定代表人：</label>
            <span>{{ supplierInfo.legalRepresentative }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>联系人：</label>
            <span>{{ supplierInfo.contact }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>联系电话：</label>
            <span>{{ supplierInfo.phone }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- IATF专项数据 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>IATF 专项数据</span>
          <el-button type="success" size="small">上传证书</el-button>
        </div>
      </template>
      <el-table :data="certifications" style="width: 100%">
        <el-table-column prop="name" label="认证名称" />
        <el-table-column prop="number" label="证书编号" />
        <el-table-column prop="issueDate" label="颁发日期" />
        <el-table-column prop="expiryDate" label="到期日期" />
        <el-table-column prop="status" label="状态">
          <template #default="scope">
            <el-tag :type="scope.row.status === 'valid' ? 'success' : 'warning'">
              {{ scope.row.status === 'valid' ? '有效' : '即将到期' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="primary" size="small">查看</el-button>
            <el-button type="warning" size="small">更新</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 动态档案 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>动态档案</span>
          <el-button type="info" size="small">添加记录</el-button>
        </div>
      </template>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="合作历史" name="cooperation">
          <el-table :data="cooperationHistory" style="width: 100%">
            <el-table-column prop="project" label="项目名称" />
            <el-table-column prop="product" label="供货产品" />
            <el-table-column prop="startDate" label="开始时间" />
            <el-table-column prop="status" label="项目状态">
              <template #default="scope">
                <el-tag :type="getProjectStatusType(scope.row.status)">
                  {{ scope.row.status }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="风险评估" name="risk">
          <div class="risk-assessment">
            <el-row :gutter="20">
              <el-col :span="12">
                <div class="risk-item">
                  <label>管理水平等级：</label>
                  <el-tag type="success">优秀</el-tag>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="risk-item">
                  <label>零件重要度等级：</label>
                  <el-tag type="danger">A级关键件</el-tag>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="risk-item">
                  <label>信用评级：</label>
                  <el-tag type="primary">AAA</el-tag>
                </div>
              </el-col>
              <el-col :span="12">
                <div class="risk-item">
                  <label>风险标记：</label>
                  <el-tag type="success">正常</el-tag>
                </div>
              </el-col>
            </el-row>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 文档中心 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>文档中心</span>
          <el-button type="primary" size="small">上传文档</el-button>
        </div>
      </template>
      <el-table :data="documents" style="width: 100%">
        <el-table-column prop="name" label="文档名称" />
        <el-table-column prop="type" label="文档类型" />
        <el-table-column prop="version" label="版本" />
        <el-table-column prop="uploadDate" label="上传时间" />
        <el-table-column label="操作">
          <template #default="scope">
            <el-button type="primary" size="small">预览</el-button>
            <el-button type="success" size="small">下载</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive } from 'vue'

export default {
  name: 'SupplierDetail',
  setup() {
    const activeTab = ref('cooperation')
    
    const supplierInfo = reactive({
      code: 'SUP001',
      name: '博世汽车部件(苏州)有限公司',
      registeredCapital: '5000万美元',
      legalRepresentative: '张三',
      contact: '张经理',
      phone: '0512-12345678'
    })

    const certifications = ref([
      {
        name: 'IATF 16949',
        number: 'IATF-2024-001',
        issueDate: '2021-01-15',
        expiryDate: '2024-01-15',
        status: 'warning'
      },
      {
        name: 'ISO 9001',
        number: 'ISO-2024-002',
        issueDate: '2022-03-20',
        expiryDate: '2025-03-20',
        status: 'valid'
      },
      {
        name: 'ISO 14001',
        number: 'ISO-2024-003',
        issueDate: '2022-06-10',
        expiryDate: '2025-06-10',
        status: 'valid'
      }
    ])

    const cooperationHistory = ref([
      {
        project: '新能源汽车制动系统',
        product: '制动盘',
        startDate: '2023-01-01',
        status: '进行中'
      },
      {
        project: '传统燃油车制动系统',
        product: '制动片',
        startDate: '2022-06-01',
        status: '已完成'
      }
    ])

    const documents = ref([
      {
        name: '质量协议',
        type: '合同协议',
        version: 'V1.2',
        uploadDate: '2024-01-15'
      },
      {
        name: '审核报告',
        type: '审核文档',
        version: 'V2.0',
        uploadDate: '2024-02-20'
      },
      {
        name: 'ROHS声明',
        type: '环保合规',
        version: 'V1.0',
        uploadDate: '2024-03-10'
      }
    ])

    const getProjectStatusType = (status) => {
      const types = {
        '进行中': 'primary',
        '已完成': 'success',
        '已暂停': 'warning',
        '已取消': 'danger'
      }
      return types[status] || ''
    }

    return {
      activeTab,
      supplierInfo,
      certifications,
      cooperationHistory,
      documents,
      getProjectStatusType
    }
  }
}
</script>

<style scoped>
.supplier-detail {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.risk-assessment {
  padding: 20px 0;
}

.risk-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
}

.risk-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
  min-width: 120px;
}
</style>
