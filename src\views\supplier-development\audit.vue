<template>
  <div class="audit-detail">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="$router.go(-1)" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>现场审核 - {{ auditInfo.supplierName }}</h2>
    </div>

    <!-- 审核基本信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>审核基本信息</span>
          <el-tag :type="getStatusType(auditInfo.status)">
            {{ getStatusText(auditInfo.status) }}
          </el-tag>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>审核编号：</label>
            <span>{{ auditInfo.auditNo }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>供应商名称：</label>
            <span>{{ auditInfo.supplierName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>审核类型：</label>
            <span>{{ auditInfo.auditType }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>计划审核时间：</label>
            <span>{{ auditInfo.plannedDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>实际审核时间：</label>
            <span>{{ auditInfo.actualDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>审核组长：</label>
            <span>{{ auditInfo.auditLeader }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 审核团队 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>审核团队</span>
          <el-button type="primary" size="small">
            <el-icon><Plus /></el-icon>
            添加成员
          </el-button>
        </div>
      </template>
      <el-table :data="auditTeam" style="width: 100%">
        <el-table-column prop="name" label="姓名" width="120" />
        <el-table-column prop="department" label="部门" width="120" />
        <el-table-column prop="role" label="角色" width="120" />
        <el-table-column prop="specialty" label="专业领域" min-width="200" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="danger" size="small">移除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 审核清单 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>审核清单</span>
          <div>
            <el-button type="success" size="small" @click="handleBatchScore">
              批量打分
            </el-button>
            <el-button type="primary" size="small" @click="handleAddItem">
              <el-icon><Plus /></el-icon>
              添加项目
            </el-button>
          </div>
        </div>
      </template>
      <el-table :data="auditItems" style="width: 100%">
        <el-table-column type="expand">
          <template #default="props">
            <div class="expand-content">
              <p><strong>检查要点：</strong>{{ props.row.checkPoints }}</p>
              <p><strong>审核发现：</strong>{{ props.row.findings }}</p>
              <p><strong>改进建议：</strong>{{ props.row.suggestions }}</p>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="category" label="审核类别" width="120" />
        <el-table-column prop="item" label="审核项目" min-width="200" />
        <el-table-column prop="requirement" label="要求" width="100" />
        <el-table-column prop="score" label="得分" width="100">
          <template #default="scope">
            <el-input-number
              v-model="scope.row.score"
              :min="0"
              :max="scope.row.maxScore"
              size="small"
              @change="handleScoreChange(scope.row)"
            />
          </template>
        </el-table-column>
        <el-table-column prop="maxScore" label="满分" width="80" />
        <el-table-column prop="auditor" label="审核员" width="100" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getItemStatusType(scope.row.status)">
              {{ getItemStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" size="small" @click="handleEditItem(scope.row)">
              编辑
            </el-button>
            <el-button type="success" size="small" @click="handlePhotoUpload(scope.row)">
              照片
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 审核总结 -->
    <el-card class="info-card">
      <template #header>
        <span>审核总结</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="12">
          <div class="summary-card">
            <h4>总体评分</h4>
            <div class="score-display">
              <span class="total-score">{{ totalScore }}</span>
              <span class="score-unit">分</span>
            </div>
            <el-progress :percentage="scorePercentage" :color="getScoreColor(scorePercentage)" />
          </div>
        </el-col>
        <el-col :span="12">
          <div class="summary-card">
            <h4>审核结论</h4>
            <el-select v-model="auditConclusion" placeholder="请选择审核结论" style="width: 100%">
              <el-option label="通过" value="pass" />
              <el-option label="有条件通过" value="conditional" />
              <el-option label="不通过" value="fail" />
            </el-select>
            <div class="conclusion-note">
              <el-input
                v-model="conclusionNote"
                type="textarea"
                :rows="3"
                placeholder="请输入审核结论说明"
                style="margin-top: 10px"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 现场照片 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>现场照片</span>
          <el-button type="primary" size="small">
            <el-icon><Camera /></el-icon>
            上传照片
          </el-button>
        </div>
      </template>
      <div class="photo-gallery">
        <div v-for="photo in photos" :key="photo.id" class="photo-item">
          <img :src="photo.url" :alt="photo.description" />
          <div class="photo-info">
            <p class="photo-title">{{ photo.title }}</p>
            <p class="photo-desc">{{ photo.description }}</p>
            <p class="photo-time">{{ photo.uploadTime }}</p>
          </div>
        </div>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-footer">
      <el-button type="success" @click="handleSubmitReport">
        <el-icon><Check /></el-icon>
        提交审核报告
      </el-button>
      <el-button type="primary" @click="handleSaveDraft">
        <el-icon><Document /></el-icon>
        保存草稿
      </el-button>
      <el-button type="warning" @click="handleExportReport">
        <el-icon><Download /></el-icon>
        导出报告
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'

export default {
  name: 'SupplierAudit',
  setup() {
    const auditConclusion = ref('')
    const conclusionNote = ref('')

    const auditInfo = reactive({
      auditNo: 'AUDIT2024001',
      supplierName: '上海精密制造有限公司',
      auditType: '新供应商准入审核',
      plannedDate: '2024-02-15',
      actualDate: '2024-02-15',
      auditLeader: '张工程师',
      status: 'ongoing'
    })

    const auditTeam = ref([
      {
        name: '张工程师',
        department: '质量部',
        role: '审核组长',
        specialty: '质量管理体系',
        phone: '13800138001'
      },
      {
        name: '李经理',
        department: '采购部',
        role: '审核员',
        specialty: '供应商管理',
        phone: '13800138002'
      },
      {
        name: '王技师',
        department: '技术部',
        role: '审核员',
        specialty: '生产工艺',
        phone: '13800138003'
      }
    ])

    const auditItems = ref([
      {
        id: 1,
        category: '质量体系',
        item: 'ISO 9001体系运行',
        requirement: '符合',
        score: 85,
        maxScore: 100,
        auditor: '张工程师',
        status: 'completed',
        checkPoints: '检查质量手册、程序文件、作业指导书的完整性和有效性',
        findings: '质量体系文件齐全，运行有效，但部分记录填写不够规范',
        suggestions: '建议加强质量记录的培训，确保记录的准确性和完整性'
      },
      {
        id: 2,
        category: '生产管理',
        item: '生产现场5S管理',
        requirement: '符合',
        score: 78,
        maxScore: 100,
        auditor: '王技师',
        status: 'completed',
        checkPoints: '检查生产现场的整理、整顿、清扫、清洁、素养情况',
        findings: '现场整体较好，但部分区域物料摆放不够整齐',
        suggestions: '建议制定更详细的5S标准，定期进行5S检查'
      },
      {
        id: 3,
        category: '设备管理',
        item: '关键设备维护保养',
        requirement: '符合',
        score: 92,
        maxScore: 100,
        auditor: '王技师',
        status: 'completed',
        checkPoints: '检查设备维护保养计划、记录和设备状态',
        findings: '设备维护保养制度完善，执行到位，设备状态良好',
        suggestions: '继续保持现有水平，建议增加预防性维护项目'
      },
      {
        id: 4,
        category: '人员管理',
        item: '员工培训体系',
        requirement: '符合',
        score: 80,
        maxScore: 100,
        auditor: '李经理',
        status: 'pending',
        checkPoints: '检查员工培训计划、培训记录和技能评估',
        findings: '培训体系基本完善，但缺少技能评估记录',
        suggestions: '建议建立完整的技能评估体系，定期评估员工技能水平'
      }
    ])

    const photos = ref([
      {
        id: 1,
        title: '生产车间全景',
        description: '生产车间整体布局，设备摆放整齐',
        url: '/api/placeholder/300/200',
        uploadTime: '2024-02-15 09:30'
      },
      {
        id: 2,
        title: '质量检测区域',
        description: '质量检测设备和检测流程',
        url: '/api/placeholder/300/200',
        uploadTime: '2024-02-15 10:15'
      },
      {
        id: 3,
        title: '仓储管理区域',
        description: '原材料和成品仓储管理情况',
        url: '/api/placeholder/300/200',
        uploadTime: '2024-02-15 11:00'
      }
    ])

    // 计算总分
    const totalScore = computed(() => {
      return auditItems.value.reduce((sum, item) => sum + item.score, 0)
    })

    const scorePercentage = computed(() => {
      const maxTotal = auditItems.value.reduce((sum, item) => sum + item.maxScore, 0)
      return Math.round((totalScore.value / maxTotal) * 100)
    })

    // 状态相关方法
    const getStatusType = (status) => {
      const types = {
        planned: 'info',
        ongoing: 'warning',
        completed: 'success',
        cancelled: 'danger'
      }
      return types[status] || ''
    }

    const getStatusText = (status) => {
      const texts = {
        planned: '计划中',
        ongoing: '进行中',
        completed: '已完成',
        cancelled: '已取消'
      }
      return texts[status] || status
    }

    const getItemStatusType = (status) => {
      const types = {
        pending: 'warning',
        completed: 'success',
        failed: 'danger'
      }
      return types[status] || ''
    }

    const getItemStatusText = (status) => {
      const texts = {
        pending: '待审核',
        completed: '已完成',
        failed: '不合格'
      }
      return texts[status] || status
    }

    const getScoreColor = (percentage) => {
      if (percentage >= 90) return '#67C23A'
      if (percentage >= 80) return '#409EFF'
      if (percentage >= 70) return '#E6A23C'
      return '#F56C6C'
    }

    // 事件处理方法
    const handleScoreChange = (row) => {
      console.log('分数变更', row)
    }

    const handleBatchScore = () => {
      console.log('批量打分')
    }

    const handleAddItem = () => {
      console.log('添加审核项目')
    }

    const handleEditItem = (row) => {
      console.log('编辑审核项目', row)
    }

    const handlePhotoUpload = (row) => {
      console.log('上传照片', row)
    }

    const handleSubmitReport = () => {
      console.log('提交审核报告')
    }

    const handleSaveDraft = () => {
      console.log('保存草稿')
    }

    const handleExportReport = () => {
      console.log('导出报告')
    }

    return {
      auditConclusion,
      conclusionNote,
      auditInfo,
      auditTeam,
      auditItems,
      photos,
      totalScore,
      scorePercentage,
      getStatusType,
      getStatusText,
      getItemStatusType,
      getItemStatusText,
      getScoreColor,
      handleScoreChange,
      handleBatchScore,
      handleAddItem,
      handleEditItem,
      handlePhotoUpload,
      handleSubmitReport,
      handleSaveDraft,
      handleExportReport
    }
  }
}
</script>

<style scoped>
.audit-detail {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.expand-content {
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 4px;
  margin: 10px 0;
}

.expand-content p {
  margin: 8px 0;
  line-height: 1.5;
}

.summary-card {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  text-align: center;
}

.summary-card h4 {
  margin-bottom: 15px;
  color: #303133;
}

.score-display {
  margin-bottom: 15px;
}

.total-score {
  font-size: 48px;
  font-weight: bold;
  color: #303133;
}

.score-unit {
  font-size: 16px;
  color: #909399;
  margin-left: 4px;
}

.conclusion-note {
  margin-top: 15px;
}

.photo-gallery {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
}

.photo-item {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  overflow: hidden;
}

.photo-item img {
  width: 100%;
  height: 200px;
  object-fit: cover;
}

.photo-info {
  padding: 15px;
}

.photo-title {
  font-weight: bold;
  color: #303133;
  margin-bottom: 5px;
}

.photo-desc {
  color: #606266;
  font-size: 14px;
  margin-bottom: 5px;
}

.photo-time {
  color: #909399;
  font-size: 12px;
}

.action-footer {
  margin-top: 30px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.action-footer .el-button {
  margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .photo-gallery {
    grid-template-columns: 1fr;
  }

  .action-footer .el-button {
    margin: 5px;
    width: calc(50% - 10px);
  }
}
</style>
