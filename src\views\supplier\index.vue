<template>
  <div class="supplier-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>供应商档案管理</h2>
      <p>建立供应商信息的"单一真相来源"，确保数据完整、准确、及时更新</p>
    </div>

    <!-- 搜索和操作区域 -->
    <el-card class="search-card">
      <el-row :gutter="20">
        <el-col :span="6">
          <el-input
            v-model="searchForm.name"
            placeholder="请输入供应商名称"
            clearable
            @keyup.enter="handleSearch"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.status"
            placeholder="请选择状态"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="正常" value="normal" />
            <el-option label="暂停" value="suspended" />
            <el-option label="黑名单" value="blacklist" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select
            v-model="searchForm.level"
            placeholder="请选择管理水平等级"
            clearable
          >
            <el-option label="全部" value="" />
            <el-option label="优秀" value="excellent" />
            <el-option label="良好" value="good" />
            <el-option label="一般" value="average" />
            <el-option label="需提升" value="improvement" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
          <el-button type="success" @click="handleAdd">
            <el-icon><Plus /></el-icon>
            新增
          </el-button>
        </el-col>
      </el-row>
    </el-card>

    <!-- 数据表格 -->
    <el-card class="table-card">
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        row-key="id"
      >
        <el-table-column prop="code" label="供应商编码" width="120" />
        <el-table-column prop="name" label="供应商名称" min-width="200" />
        <el-table-column prop="contact" label="联系人" width="100" />
        <el-table-column prop="phone" label="联系电话" width="130" />
        <el-table-column prop="status" label="状态" width="80">
          <template #default="scope">
            <el-tag
              :type="getStatusType(scope.row.status)"
              size="small"
            >
              {{ getStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="level" label="管理水平等级" width="120">
          <template #default="scope">
            <el-tag
              :type="getLevelType(scope.row.level)"
              size="small"
            >
              {{ getLevelText(scope.row.level) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="partLevel" label="零件重要度" width="100">
          <template #default="scope">
            <el-tag
              :type="getPartLevelType(scope.row.partLevel)"
              size="small"
            >
              {{ scope.row.partLevel }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="certExpiry" label="认证到期时间" width="120" />
        <el-table-column label="操作" width="280" fixed="right">
          <template #default="scope">
            <div class="action-buttons">
              <el-button
                type="primary"
                size="small"
                @click="handleView(scope.row)"
              >
                查看
              </el-button>
              <el-button
                type="success"
                size="small"
                @click="handleEdit(scope.row)"
              >
                编辑
              </el-button>
              <el-button
                type="warning"
                size="small"
                @click="handleAudit(scope.row)"
              >
                审核
              </el-button>
              <el-button
                type="info"
                size="small"
                @click="handleDocs(scope.row)"
              >
                文档
              </el-button>
              <el-dropdown @command="handleCommand">
                <el-button type="primary" size="small">
                  更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item :command="{action: 'suspend', row: scope.row}">
                      暂停合作
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'blacklist', row: scope.row}">
                      加入黑名单
                    </el-dropdown-item>
                    <el-dropdown-item :command="{action: 'delete', row: scope.row}" divided>
                      删除
                    </el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

export default {
  name: 'SupplierIndex',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    
    // 搜索表单
    const searchForm = reactive({
      name: '',
      status: '',
      level: ''
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 表格数据
    const tableData = ref([])

    // 初始化示例数据
    const initData = () => {
      tableData.value = [
        {
          id: 1,
          code: 'SUP001',
          name: '博世汽车部件(苏州)有限公司',
          contact: '张经理',
          phone: '0512-12345678',
          status: 'normal',
          level: 'excellent',
          partLevel: 'A级',
          certExpiry: '2024-12-31'
        },
        {
          id: 2,
          code: 'SUP002', 
          name: '大陆汽车系统(常熟)有限公司',
          contact: '李总监',
          phone: '0512-87654321',
          status: 'normal',
          level: 'good',
          partLevel: 'A级',
          certExpiry: '2024-10-15'
        },
        {
          id: 3,
          code: 'SUP003',
          name: '法雷奥汽车空调湖北有限公司',
          contact: '王主管',
          phone: '027-11223344',
          status: 'suspended',
          level: 'average',
          partLevel: 'B级',
          certExpiry: '2024-08-20'
        },
        {
          id: 4,
          code: 'SUP004',
          name: '麦格纳汽车技术(上海)有限公司',
          contact: '刘工程师',
          phone: '021-55667788',
          status: 'normal',
          level: 'excellent',
          partLevel: 'A级',
          certExpiry: '2025-03-10'
        },
        {
          id: 5,
          code: 'SUP005',
          name: '安波福电气系统有限公司',
          contact: '陈经理',
          phone: '0755-99887766',
          status: 'blacklist',
          level: 'improvement',
          partLevel: 'C级',
          certExpiry: '2024-06-30'
        }
      ]
      pagination.total = tableData.value.length
    }

    // 状态相关方法
    const getStatusType = (status) => {
      const types = {
        normal: 'success',
        suspended: 'warning', 
        blacklist: 'danger'
      }
      return types[status] || ''
    }

    const getStatusText = (status) => {
      const texts = {
        normal: '正常',
        suspended: '暂停',
        blacklist: '黑名单'
      }
      return texts[status] || status
    }

    const getLevelType = (level) => {
      const types = {
        excellent: 'success',
        good: 'primary',
        average: 'warning',
        improvement: 'danger'
      }
      return types[level] || ''
    }

    const getLevelText = (level) => {
      const texts = {
        excellent: '优秀',
        good: '良好', 
        average: '一般',
        improvement: '需提升'
      }
      return texts[level] || level
    }

    const getPartLevelType = (partLevel) => {
      const types = {
        'A级': 'danger',
        'B级': 'warning',
        'C级': 'info'
      }
      return types[partLevel] || ''
    }

    // 事件处理方法
    const handleSearch = () => {
      console.log('搜索', searchForm)
    }

    const handleReset = () => {
      Object.keys(searchForm).forEach(key => {
        searchForm[key] = ''
      })
    }

    const handleAdd = () => {
      console.log('新增供应商')
    }

    const handleView = (row) => {
      router.push(`/supplier/detail/${row.id}`)
    }

    const handleEdit = (row) => {
      console.log('编辑', row)
    }

    const handleAudit = (row) => {
      console.log('审核', row)
    }

    const handleDocs = (row) => {
      console.log('文档管理', row)
    }

    const handleCommand = (command) => {
      console.log('执行操作', command)
    }

    const handleSizeChange = (val) => {
      pagination.pageSize = val
    }

    const handleCurrentChange = (val) => {
      pagination.currentPage = val
    }

    onMounted(() => {
      initData()
    })

    return {
      loading,
      searchForm,
      pagination,
      tableData,
      getStatusType,
      getStatusText,
      getLevelType,
      getLevelText,
      getPartLevelType,
      handleSearch,
      handleReset,
      handleAdd,
      handleView,
      handleEdit,
      handleAudit,
      handleDocs,
      handleCommand,
      handleSizeChange,
      handleCurrentChange
    }
  }
}
</script>

<style scoped>
.supplier-container {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #909399;
  font-size: 14px;
}

.search-card {
  margin-bottom: 20px;
}

.table-card {
  margin-bottom: 20px;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  min-width: auto;
  padding: 5px 8px;
  font-size: 12px;
}

.action-buttons .el-dropdown {
  margin-left: 4px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .action-buttons .el-button {
    padding: 4px 6px;
    font-size: 11px;
  }
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}
</style>
