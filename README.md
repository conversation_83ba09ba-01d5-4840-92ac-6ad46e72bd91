# 供应商质量管理系统

基于Vue 3 + Element Plus的供应商质量管理系统原型图，符合IATF 16949标准要求。

## 功能特性

### 🏢 供应商档案管理
- **基础信息管理**：企业概况、联系信息、组织架构
- **IATF专项数据**：认证资质、环保合规、法律合规
- **动态档案**：合作历史、风险标记、等级评估
- **文档中心**：资质证书、审核报告、合同协议等文档管理

### 🚀 供应商开发与准入管理
- **新供应商准入流程**：在线申请、初步审核、风险评估、现场审核、试生产跟踪、准入决策
- **二供开发管理**：需求识别、计划制定、专属开发流程、历史问题闭环验证、深度审查
- **供应商退出流程**：触发条件、退出评估、决策通知、善后管理
- **流程跟踪**：完整的流程进度跟踪和状态管理

### 📋 核心功能
- **搜索筛选**：按供应商名称、状态、管理水平等级筛选
- **新增/编辑**：完整的供应商信息录入和编辑功能
- **状态管理**：正常/暂停/黑名单状态管理
- **等级评估**：管理水平等级和零件重要度等级
- **到期提醒**：认证证书到期预警机制
- **准入管理**：新供应商准入申请、审核、评估全流程管理
- **现场审核**：支持移动端离线填写、现场照片上传、审核清单管理
- **二供开发**：战略性二供开发计划制定和执行跟踪
- **退出管理**：供应商退出流程和善后处理

### 🎨 界面特性
- **响应式设计**：适配不同屏幕尺寸
- **操作列自适应**：按钮合理布局，支持更多操作下拉菜单
- **数据表格**：支持分页、排序、筛选
- **弹窗表单**：新增和编辑使用弹窗形式，用户体验友好

## 技术栈

- **前端框架**：Vue 3 (Composition API)
- **构建工具**：Vite
- **UI组件库**：Element Plus
- **路由管理**：Vue Router 4
- **状态管理**：Pinia
- **图标库**：Element Plus Icons

## 快速开始

### 安装依赖
```bash
npm install
```

### 启动开发服务器
```bash
npm run dev
```

### 构建生产版本
```bash
npm run build
```

## 项目结构

```
src/
├── components/          # 公共组件
├── layout/             # 布局组件
│   └── index.vue       # 主布局
├── router/             # 路由配置
│   └── index.js        # 路由定义
├── views/              # 页面组件
│   ├── supplier/       # 供应商相关页面
│   │   ├── index.vue   # 供应商列表
│   │   └── detail.vue  # 供应商详情
│   └── supplier-development/  # 供应商开发与准入
│       ├── index.vue          # 开发与准入主页
│       ├── application.vue    # 准入申请详情
│       └── audit.vue          # 现场审核
├── App.vue             # 根组件
└── main.js             # 入口文件
```

## 示例数据

系统预置了5条供应商示例数据，包含：
- 博世汽车部件(苏州)有限公司
- 大陆汽车系统(常熟)有限公司  
- 法雷奥汽车空调湖北有限公司
- 麦格纳汽车技术(上海)有限公司
- 安波福电气系统有限公司

每条数据包含完整的基础信息、认证状态、管理等级等信息。

## 页面功能

### 供应商列表页面
- 搜索和筛选功能
- 数据表格展示
- 新增供应商
- 编辑供应商信息
- 查看供应商详情
- 审核、文档管理等操作

### 供应商详情页面
- 基础信息展示
- IATF专项数据管理
- 动态档案（合作历史、风险评估）
- 文档中心

### 供应商开发与准入管理页面
- 统计卡片展示（待审核申请、现场审核中、试生产跟踪、二供开发中）
- 新供应商准入管理（申请列表、状态跟踪、进度管理）
- 二供开发管理（项目管理、开发计划、进度跟踪）
- 供应商退出管理（退出申请、评估审批、执行跟踪）

### 准入申请详情页面
- 申请基本信息展示
- 流程进度跟踪（6步骤流程）
- 企业信息详细展示
- 评估结果管理（多部门评估打分）
- 风险评估（财务、技术、质量、合规四个维度）
- 文档资料管理

### 现场审核页面
- 审核基本信息管理
- 审核团队配置
- 审核清单管理（可展开详情、实时打分）
- 审核总结（总体评分、审核结论）
- 现场照片管理
- 审核报告导出

## 开发说明

这是一个原型图项目，主要用于界面展示和交互演示。所有的数据操作都是前端模拟，没有连接真实的后端API。

如需要连接真实后端，可以：
1. 替换示例数据为API调用
2. 添加真实的表单验证
3. 实现文件上传功能
4. 添加用户权限管理

## 浏览器支持

- Chrome >= 87
- Firefox >= 78
- Safari >= 14
- Edge >= 88
