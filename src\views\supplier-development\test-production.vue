<template>
  <div class="test-production">
    <!-- 页面标题 -->
    <div class="page-header">
      <el-button @click="$router.go(-1)" type="primary" plain>
        <el-icon><ArrowLeft /></el-icon>
        返回列表
      </el-button>
      <h2>试生产跟踪 - {{ productionInfo.supplierName }}</h2>
    </div>

    <!-- 基本信息 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>试生产基本信息</span>
          <el-tag :type="getStatusType(productionInfo.status)">
            {{ getStatusText(productionInfo.status) }}
          </el-tag>
        </div>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="info-item">
            <label>试产编号：</label>
            <span>{{ productionInfo.productionNo }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>供应商名称：</label>
            <span>{{ productionInfo.supplierName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>产品名称：</label>
            <span>{{ productionInfo.productName }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>计划开始时间：</label>
            <span>{{ productionInfo.plannedStartDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>实际开始时间：</label>
            <span>{{ productionInfo.actualStartDate }}</span>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="info-item">
            <label>预计完成时间：</label>
            <span>{{ productionInfo.expectedEndDate }}</span>
          </div>
        </el-col>
      </el-row>
    </el-card>

    <!-- 试产计划 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>试产计划</span>
          <el-button type="primary" size="small">
            <el-icon><Plus /></el-icon>
            添加计划
          </el-button>
        </div>
      </template>
      <el-table :data="productionPlan" style="width: 100%">
        <el-table-column prop="phase" label="阶段" width="120" />
        <el-table-column prop="task" label="任务内容" min-width="200" />
        <el-table-column prop="plannedQuantity" label="计划数量" width="100" />
        <el-table-column prop="actualQuantity" label="实际数量" width="100" />
        <el-table-column prop="plannedDate" label="计划时间" width="120" />
        <el-table-column prop="actualDate" label="实际时间" width="120" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getPlanStatusType(scope.row.status)">
              {{ getPlanStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" size="small">编辑</el-button>
            <el-button type="success" size="small">完成</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 质量跟踪 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>质量跟踪</span>
          <el-button type="success" size="small">
            <el-icon><Plus /></el-icon>
            添加检测记录
          </el-button>
        </div>
      </template>
      <el-table :data="qualityRecords" style="width: 100%">
        <el-table-column prop="batchNo" label="批次号" width="120" />
        <el-table-column prop="inspectionDate" label="检测日期" width="120" />
        <el-table-column prop="inspectionItem" label="检测项目" min-width="150" />
        <el-table-column prop="standard" label="标准要求" width="120" />
        <el-table-column prop="actualValue" label="实测值" width="100" />
        <el-table-column prop="result" label="检测结果" width="100">
          <template #default="scope">
            <el-tag :type="getQualityResultType(scope.row.result)">
              {{ scope.row.result }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="inspector" label="检测员" width="100" />
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="small">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 交付跟踪 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>交付跟踪</span>
          <el-button type="warning" size="small">
            <el-icon><Plus /></el-icon>
            添加交付记录
          </el-button>
        </div>
      </template>
      <el-table :data="deliveryRecords" style="width: 100%">
        <el-table-column prop="deliveryNo" label="交付单号" width="140" />
        <el-table-column prop="plannedDate" label="计划交付时间" width="130" />
        <el-table-column prop="actualDate" label="实际交付时间" width="130" />
        <el-table-column prop="plannedQuantity" label="计划数量" width="100" />
        <el-table-column prop="actualQuantity" label="实际数量" width="100" />
        <el-table-column prop="onTimeRate" label="准时率" width="100">
          <template #default="scope">
            <el-tag :type="getOnTimeRateType(scope.row.onTimeRate)">
              {{ scope.row.onTimeRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="qualityRate" label="合格率" width="100">
          <template #default="scope">
            <el-tag :type="getQualityRateType(scope.row.qualityRate)">
              {{ scope.row.qualityRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100">
          <template #default="scope">
            <el-button type="primary" size="small">查看</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 问题跟踪 -->
    <el-card class="info-card">
      <template #header>
        <div class="card-header">
          <span>问题跟踪</span>
          <el-button type="danger" size="small">
            <el-icon><Plus /></el-icon>
            添加问题
          </el-button>
        </div>
      </template>
      <el-table :data="issueRecords" style="width: 100%">
        <el-table-column prop="issueNo" label="问题编号" width="120" />
        <el-table-column prop="issueDate" label="发现时间" width="120" />
        <el-table-column prop="issueType" label="问题类型" width="100" />
        <el-table-column prop="description" label="问题描述" min-width="200" />
        <el-table-column prop="severity" label="严重程度" width="100">
          <template #default="scope">
            <el-tag :type="getSeverityType(scope.row.severity)">
              {{ scope.row.severity }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag :type="getIssueStatusType(scope.row.status)">
              {{ getIssueStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="assignee" label="负责人" width="100" />
        <el-table-column label="操作" width="150">
          <template #default="scope">
            <el-button type="primary" size="small">查看</el-button>
            <el-button type="success" size="small">处理</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 试产总结 -->
    <el-card class="info-card">
      <template #header>
        <span>试产总结</span>
      </template>
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="summary-card">
            <h4>总体进度</h4>
            <div class="progress-display">
              <el-progress :percentage="overallProgress" :stroke-width="8" />
              <span class="progress-text">{{ overallProgress }}%</span>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-card">
            <h4>质量表现</h4>
            <div class="quality-display">
              <div class="metric">
                <span class="label">平均合格率：</span>
                <span class="value">{{ averageQualityRate }}%</span>
              </div>
              <div class="metric">
                <span class="label">问题数量：</span>
                <span class="value">{{ totalIssues }}个</span>
              </div>
            </div>
          </div>
        </el-col>
        <el-col :span="8">
          <div class="summary-card">
            <h4>交付表现</h4>
            <div class="delivery-display">
              <div class="metric">
                <span class="label">平均准时率：</span>
                <span class="value">{{ averageOnTimeRate }}%</span>
              </div>
              <div class="metric">
                <span class="label">交付批次：</span>
                <span class="value">{{ totalDeliveries }}批</span>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      
      <div class="conclusion-section">
        <h4>试产结论</h4>
        <el-select v-model="productionConclusion" placeholder="请选择试产结论" style="width: 300px">
          <el-option label="通过，可批量生产" value="pass" />
          <el-option label="有条件通过" value="conditional" />
          <el-option label="不通过，需改进" value="fail" />
        </el-select>
        <div class="conclusion-note">
          <el-input
            v-model="conclusionNote"
            type="textarea"
            :rows="4"
            placeholder="请输入试产结论说明"
            style="margin-top: 15px"
          />
        </div>
      </div>
    </el-card>

    <!-- 操作按钮 -->
    <div class="action-footer">
      <el-button type="success" @click="handleCompleteProduction">
        <el-icon><Check /></el-icon>
        完成试产
      </el-button>
      <el-button type="primary" @click="handleSaveProgress">
        <el-icon><Document /></el-icon>
        保存进度
      </el-button>
      <el-button type="warning" @click="handleExportReport">
        <el-icon><Download /></el-icon>
        导出报告
      </el-button>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed } from 'vue'

export default {
  name: 'TestProduction',
  setup() {
    const productionConclusion = ref('')
    const conclusionNote = ref('')
    const overallProgress = ref(75)

    const productionInfo = reactive({
      productionNo: '*********',
      supplierName: '上海精密制造有限公司',
      productName: '制动盘总成',
      plannedStartDate: '2024-02-20',
      actualStartDate: '2024-02-22',
      expectedEndDate: '2024-03-20',
      status: 'testing'
    })

    const productionPlan = ref([
      {
        phase: '准备阶段',
        task: '原材料准备和设备调试',
        plannedQuantity: 100,
        actualQuantity: 100,
        plannedDate: '2024-02-20',
        actualDate: '2024-02-22',
        status: 'completed'
      },
      {
        phase: '试产阶段',
        task: '小批量试生产',
        plannedQuantity: 500,
        actualQuantity: 350,
        plannedDate: '2024-02-25',
        actualDate: '2024-02-28',
        status: 'ongoing'
      },
      {
        phase: '验证阶段',
        task: '产品质量验证',
        plannedQuantity: 200,
        actualQuantity: 0,
        plannedDate: '2024-03-05',
        actualDate: '',
        status: 'pending'
      }
    ])

    const qualityRecords = ref([
      {
        batchNo: 'B001',
        inspectionDate: '2024-02-25',
        inspectionItem: '尺寸检测',
        standard: '±0.1mm',
        actualValue: '0.05mm',
        result: '合格',
        inspector: '张检测员'
      },
      {
        batchNo: 'B002',
        inspectionDate: '2024-02-28',
        inspectionItem: '硬度测试',
        standard: 'HRC45-50',
        actualValue: 'HRC47',
        result: '合格',
        inspector: '李检测员'
      },
      {
        batchNo: 'B003',
        inspectionDate: '2024-03-01',
        inspectionItem: '表面粗糙度',
        standard: 'Ra≤3.2',
        actualValue: 'Ra2.8',
        result: '合格',
        inspector: '王检测员'
      }
    ])

    const deliveryRecords = ref([
      {
        deliveryNo: 'D001',
        plannedDate: '2024-02-26',
        actualDate: '2024-02-26',
        plannedQuantity: 100,
        actualQuantity: 100,
        onTimeRate: 100,
        qualityRate: 98
      },
      {
        deliveryNo: 'D002',
        plannedDate: '2024-03-01',
        actualDate: '2024-03-02',
        plannedQuantity: 150,
        actualQuantity: 145,
        onTimeRate: 95,
        qualityRate: 96
      }
    ])

    const issueRecords = ref([
      {
        issueNo: 'I001',
        issueDate: '2024-02-27',
        issueType: '质量问题',
        description: '部分产品表面有轻微划痕',
        severity: '轻微',
        status: 'resolved',
        assignee: '质量工程师'
      },
      {
        issueNo: 'I002',
        issueDate: '2024-03-01',
        issueType: '交付问题',
        description: '交付时间延迟1天',
        severity: '一般',
        status: 'ongoing',
        assignee: '生产经理'
      }
    ])

    // 计算属性
    const averageQualityRate = computed(() => {
      const rates = deliveryRecords.value.map(record => record.qualityRate)
      return Math.round(rates.reduce((sum, rate) => sum + rate, 0) / rates.length)
    })

    const averageOnTimeRate = computed(() => {
      const rates = deliveryRecords.value.map(record => record.onTimeRate)
      return Math.round(rates.reduce((sum, rate) => sum + rate, 0) / rates.length)
    })

    const totalIssues = computed(() => issueRecords.value.length)
    const totalDeliveries = computed(() => deliveryRecords.value.length)

    // 状态相关方法
    const getStatusType = (status) => {
      const types = {
        planning: 'info',
        testing: 'warning',
        completed: 'success',
        failed: 'danger'
      }
      return types[status] || ''
    }

    const getStatusText = (status) => {
      const texts = {
        planning: '计划中',
        testing: '试产中',
        completed: '已完成',
        failed: '失败'
      }
      return texts[status] || status
    }

    const getPlanStatusType = (status) => {
      const types = {
        pending: 'info',
        ongoing: 'warning',
        completed: 'success',
        delayed: 'danger'
      }
      return types[status] || ''
    }

    const getPlanStatusText = (status) => {
      const texts = {
        pending: '待开始',
        ongoing: '进行中',
        completed: '已完成',
        delayed: '延期'
      }
      return texts[status] || status
    }

    const getQualityResultType = (result) => {
      return result === '合格' ? 'success' : 'danger'
    }

    const getOnTimeRateType = (rate) => {
      if (rate >= 95) return 'success'
      if (rate >= 85) return 'warning'
      return 'danger'
    }

    const getQualityRateType = (rate) => {
      if (rate >= 95) return 'success'
      if (rate >= 90) return 'warning'
      return 'danger'
    }

    const getSeverityType = (severity) => {
      const types = {
        '轻微': 'info',
        '一般': 'warning',
        '严重': 'danger',
        '紧急': 'danger'
      }
      return types[severity] || ''
    }

    const getIssueStatusType = (status) => {
      const types = {
        open: 'danger',
        ongoing: 'warning',
        resolved: 'success',
        closed: 'info'
      }
      return types[status] || ''
    }

    const getIssueStatusText = (status) => {
      const texts = {
        open: '待处理',
        ongoing: '处理中',
        resolved: '已解决',
        closed: '已关闭'
      }
      return texts[status] || status
    }

    // 事件处理方法
    const handleCompleteProduction = () => {
      console.log('完成试产')
    }

    const handleSaveProgress = () => {
      console.log('保存进度')
    }

    const handleExportReport = () => {
      console.log('导出报告')
    }

    return {
      productionConclusion,
      conclusionNote,
      overallProgress,
      productionInfo,
      productionPlan,
      qualityRecords,
      deliveryRecords,
      issueRecords,
      averageQualityRate,
      averageOnTimeRate,
      totalIssues,
      totalDeliveries,
      getStatusType,
      getStatusText,
      getPlanStatusType,
      getPlanStatusText,
      getQualityResultType,
      getOnTimeRateType,
      getQualityRateType,
      getSeverityType,
      getIssueStatusType,
      getIssueStatusText,
      handleCompleteProduction,
      handleSaveProgress,
      handleExportReport
    }
  }
}
</script>

<style scoped>
.test-production {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  gap: 20px;
}

.page-header h2 {
  color: #303133;
  margin: 0;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-item {
  margin-bottom: 15px;
}

.info-item label {
  font-weight: bold;
  color: #606266;
  margin-right: 8px;
}

.summary-card {
  padding: 20px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  text-align: center;
  height: 100%;
}

.summary-card h4 {
  margin-bottom: 15px;
  color: #303133;
}

.progress-display {
  position: relative;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-weight: bold;
  color: #303133;
}

.quality-display,
.delivery-display {
  text-align: left;
}

.metric {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
}

.metric .label {
  color: #606266;
}

.metric .value {
  font-weight: bold;
  color: #303133;
}

.conclusion-section {
  margin-top: 30px;
  padding-top: 20px;
  border-top: 1px solid #e4e7ed;
}

.conclusion-section h4 {
  margin-bottom: 15px;
  color: #303133;
}

.action-footer {
  margin-top: 30px;
  text-align: center;
  padding: 20px;
  border-top: 1px solid #e4e7ed;
}

.action-footer .el-button {
  margin: 0 10px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .action-footer .el-button {
    margin: 5px;
    width: calc(50% - 10px);
  }
}
</style>
