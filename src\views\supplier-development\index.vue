<template>
  <div class="supplier-development-container">
    <!-- 页面标题 -->
    <div class="page-header">
      <h2>供应商开发与准入管理</h2>
      <p>建立标准化的供应商开发与评估流程，确保新供应商符合公司和IATF 16949的所有要求</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">12</div>
            <div class="stats-label">待审核申请</div>
          </div>
          <el-icon class="stats-icon pending"><Clock /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">8</div>
            <div class="stats-label">现场审核中</div>
          </div>
          <el-icon class="stats-icon auditing"><DocumentChecked /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">5</div>
            <div class="stats-label">试生产跟踪</div>
          </div>
          <el-icon class="stats-icon testing"><Tools /></el-icon>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-number">3</div>
            <div class="stats-label">二供开发中</div>
          </div>
          <el-icon class="stats-icon developing"><Plus /></el-icon>
        </el-card>
      </el-col>
    </el-row>

    <!-- 功能标签页 -->
    <el-card class="main-card">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <!-- 新供应商准入 -->
        <el-tab-pane label="新供应商准入" name="admission">
          <div class="tab-header">
            <el-button type="primary" @click="handleNewApplication">
              <el-icon><Plus /></el-icon>
              新增准入申请
            </el-button>
            <div class="search-area">
              <el-input
                v-model="searchForm.keyword"
                placeholder="请输入供应商名称或申请编号"
                style="width: 300px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-select
                v-model="searchForm.status"
                placeholder="申请状态"
                style="width: 150px; margin-left: 10px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="待审核" value="pending" />
                <el-option label="审核中" value="reviewing" />
                <el-option label="现场审核" value="auditing" />
                <el-option label="试生产" value="testing" />
                <el-option label="已通过" value="approved" />
                <el-option label="已拒绝" value="rejected" />
              </el-select>
              <el-button type="primary" @click="handleSearch">搜索</el-button>
            </div>
          </div>

          <el-table :data="admissionData" style="width: 100%" v-loading="loading">
            <el-table-column prop="applicationNo" label="申请编号" width="140" />
            <el-table-column prop="supplierName" label="供应商名称" min-width="200" />
            <el-table-column prop="contactPerson" label="联系人" width="100" />
            <el-table-column prop="phone" label="联系电话" width="130" />
            <el-table-column prop="applicationDate" label="申请时间" width="110" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getStatusType(scope.row.status)">
                  {{ getStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="进度" width="120">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.progress"
                  :stroke-width="6"
                  :show-text="false"
                />
                <span style="margin-left: 8px; font-size: 12px;">{{ scope.row.progress }}%</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="280" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button type="primary" size="small" @click="handleViewApplication(scope.row)">
                    查看
                  </el-button>
                  <el-button
                    type="success"
                    size="small"
                    @click="handleAudit(scope.row)"
                    v-if="scope.row.status === 'reviewing'"
                  >
                    现场审核
                  </el-button>
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleTestProduction(scope.row)"
                    v-if="scope.row.status === 'auditing'"
                  >
                    试生产
                  </el-button>
                  <el-dropdown @command="handleCommand">
                    <el-button type="primary" size="small">
                      更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
                    </el-button>
                    <template #dropdown>
                      <el-dropdown-menu>
                        <el-dropdown-item :command="{action: 'approve', row: scope.row}">
                          批准准入
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'reject', row: scope.row}">
                          拒绝准入
                        </el-dropdown-item>
                        <el-dropdown-item :command="{action: 'edit', row: scope.row}" divided>
                          编辑申请
                        </el-dropdown-item>
                      </el-dropdown-menu>
                    </template>
                  </el-dropdown>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 二供开发 -->
        <el-tab-pane label="二供开发" name="secondary">
          <div class="tab-header">
            <el-button type="success" @click="handleNewSecondary">
              <el-icon><Plus /></el-icon>
              新增二供开发
            </el-button>
            <div class="search-area">
              <el-input
                v-model="secondarySearchForm.keyword"
                placeholder="请输入物料编号或供应商名称"
                style="width: 300px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-select
                v-model="secondarySearchForm.status"
                placeholder="开发状态"
                style="width: 150px; margin-left: 10px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="计划中" value="planning" />
                <el-option label="开发中" value="developing" />
                <el-option label="验证中" value="validating" />
                <el-option label="已完成" value="completed" />
                <el-option label="已暂停" value="suspended" />
              </el-select>
              <el-button type="primary" @click="handleSecondarySearch">搜索</el-button>
            </div>
          </div>

          <el-table :data="secondaryData" style="width: 100%" v-loading="loading">
            <el-table-column prop="projectNo" label="项目编号" width="140" />
            <el-table-column prop="materialCode" label="物料编号" width="120" />
            <el-table-column prop="materialName" label="物料名称" min-width="180" />
            <el-table-column prop="targetSupplier" label="目标供应商" min-width="200" />
            <el-table-column prop="reason" label="开发原因" width="120" />
            <el-table-column prop="startDate" label="开始时间" width="110" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getSecondaryStatusType(scope.row.status)">
                  {{ getSecondaryStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="progress" label="进度" width="120">
              <template #default="scope">
                <el-progress
                  :percentage="scope.row.progress"
                  :stroke-width="6"
                  :show-text="false"
                />
                <span style="margin-left: 8px; font-size: 12px;">{{ scope.row.progress }}%</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button type="primary" size="small" @click="handleViewSecondary(scope.row)">
                    查看
                  </el-button>
                  <el-button type="success" size="small" @click="handleEditSecondary(scope.row)">
                    编辑
                  </el-button>
                  <el-button type="warning" size="small" @click="handleSecondaryAudit(scope.row)">
                    审核
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>

        <!-- 供应商退出 -->
        <el-tab-pane label="供应商退出" name="exit">
          <div class="tab-header">
            <el-button type="danger" @click="handleNewExit">
              <el-icon><Remove /></el-icon>
              新增退出申请
            </el-button>
            <div class="search-area">
              <el-input
                v-model="exitSearchForm.keyword"
                placeholder="请输入供应商名称"
                style="width: 300px"
                clearable
              >
                <template #prefix>
                  <el-icon><Search /></el-icon>
                </template>
              </el-input>
              <el-select
                v-model="exitSearchForm.status"
                placeholder="退出状态"
                style="width: 150px; margin-left: 10px"
                clearable
              >
                <el-option label="全部" value="" />
                <el-option label="申请中" value="applying" />
                <el-option label="评估中" value="evaluating" />
                <el-option label="审批中" value="approving" />
                <el-option label="执行中" value="executing" />
                <el-option label="已完成" value="completed" />
              </el-select>
              <el-button type="primary" @click="handleExitSearch">搜索</el-button>
            </div>
          </div>

          <el-table :data="exitData" style="width: 100%" v-loading="loading">
            <el-table-column prop="exitNo" label="退出编号" width="140" />
            <el-table-column prop="supplierName" label="供应商名称" min-width="200" />
            <el-table-column prop="reason" label="退出原因" width="150" />
            <el-table-column prop="applicant" label="申请人" width="100" />
            <el-table-column prop="applicationDate" label="申请时间" width="110" />
            <el-table-column prop="status" label="状态" width="100">
              <template #default="scope">
                <el-tag :type="getExitStatusType(scope.row.status)">
                  {{ getExitStatusText(scope.row.status) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="200" fixed="right">
              <template #default="scope">
                <div class="action-buttons">
                  <el-button type="primary" size="small" @click="handleViewExit(scope.row)">
                    查看
                  </el-button>
                  <el-button type="success" size="small" @click="handleEditExit(scope.row)">
                    编辑
                  </el-button>
                  <el-button type="warning" size="small" @click="handleExitApprove(scope.row)">
                    审批
                  </el-button>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
      </el-tabs>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.currentPage"
          v-model:page-size="pagination.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="pagination.total"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 新增/编辑准入申请弹窗 -->
    <el-dialog
      v-model="admissionDialogVisible"
      :title="admissionDialogTitle"
      width="800px"
      :before-close="handleAdmissionDialogClose"
    >
      <el-form
        ref="admissionFormRef"
        :model="admissionFormData"
        :rules="admissionFormRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请编号" prop="applicationNo">
              <el-input
                v-model="admissionFormData.applicationNo"
                placeholder="请输入申请编号"
                :disabled="isAdmissionEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商名称" prop="supplierName">
              <el-input
                v-model="admissionFormData.supplierName"
                placeholder="请输入供应商名称"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="联系人" prop="contactPerson">
              <el-input
                v-model="admissionFormData.contactPerson"
                placeholder="请输入联系人"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="联系电话" prop="phone">
              <el-input
                v-model="admissionFormData.phone"
                placeholder="请输入联系电话"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请时间" prop="applicationDate">
              <el-date-picker
                v-model="admissionFormData.applicationDate"
                type="date"
                placeholder="请选择申请时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="admissionFormData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="待审核" value="pending" />
                <el-option label="审核中" value="reviewing" />
                <el-option label="现场审核" value="auditing" />
                <el-option label="试生产" value="testing" />
                <el-option label="已通过" value="approved" />
                <el-option label="已拒绝" value="rejected" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="进度" prop="progress">
              <el-input-number
                v-model="admissionFormData.progress"
                :min="0"
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleAdmissionDialogClose">取消</el-button>
          <el-button type="primary" @click="handleAdmissionSubmit" :loading="admissionSubmitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 二供开发新增/编辑弹窗 -->
    <el-dialog
      v-model="secondaryDialogVisible"
      :title="secondaryDialogTitle"
      width="800px"
      :before-close="handleSecondaryDialogClose"
    >
      <el-form
        ref="secondaryFormRef"
        :model="secondaryFormData"
        :rules="secondaryFormRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="项目编号" prop="projectNo">
              <el-input
                v-model="secondaryFormData.projectNo"
                placeholder="请输入项目编号"
                :disabled="isSecondaryEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物料编号" prop="materialCode">
              <el-input
                v-model="secondaryFormData.materialCode"
                placeholder="请输入物料编号"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="物料名称" prop="materialName">
              <el-input
                v-model="secondaryFormData.materialName"
                placeholder="请输入物料名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目标供应商" prop="targetSupplier">
              <el-input
                v-model="secondaryFormData.targetSupplier"
                placeholder="请输入目标供应商"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="开发原因" prop="reason">
              <el-select
                v-model="secondaryFormData.reason"
                placeholder="请选择开发原因"
                style="width: 100%"
              >
                <el-option label="单一来源风险" value="单一来源风险" />
                <el-option label="成本优化" value="成本优化" />
                <el-option label="产能瓶颈" value="产能瓶颈" />
                <el-option label="质量提升" value="质量提升" />
                <el-option label="技术升级" value="技术升级" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间" prop="startDate">
              <el-date-picker
                v-model="secondaryFormData.startDate"
                type="date"
                placeholder="请选择开始时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="secondaryFormData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="计划中" value="planning" />
                <el-option label="开发中" value="developing" />
                <el-option label="验证中" value="validating" />
                <el-option label="已完成" value="completed" />
                <el-option label="已暂停" value="suspended" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="进度" prop="progress">
              <el-input-number
                v-model="secondaryFormData.progress"
                :min="0"
                :max="100"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleSecondaryDialogClose">取消</el-button>
          <el-button type="primary" @click="handleSecondarySubmit" :loading="secondarySubmitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 供应商退出新增/编辑弹窗 -->
    <el-dialog
      v-model="exitDialogVisible"
      :title="exitDialogTitle"
      width="800px"
      :before-close="handleExitDialogClose"
    >
      <el-form
        ref="exitFormRef"
        :model="exitFormData"
        :rules="exitFormRules"
        label-width="120px"
      >
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="退出编号" prop="exitNo">
              <el-input
                v-model="exitFormData.exitNo"
                placeholder="请输入退出编号"
                :disabled="isExitEdit"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="供应商名称" prop="supplierName">
              <el-input
                v-model="exitFormData.supplierName"
                placeholder="请输入供应商名称"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="退出原因" prop="reason">
              <el-select
                v-model="exitFormData.reason"
                placeholder="请选择退出原因"
                style="width: 100%"
              >
                <el-option label="质量问题严重" value="质量问题严重" />
                <el-option label="交付延迟频繁" value="交付延迟频繁" />
                <el-option label="成本过高" value="成本过高" />
                <el-option label="合规性问题" value="合规性问题" />
                <el-option label="战略调整" value="战略调整" />
                <el-option label="技术落后" value="技术落后" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="申请人" prop="applicant">
              <el-input
                v-model="exitFormData.applicant"
                placeholder="请输入申请人"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="申请时间" prop="applicationDate">
              <el-date-picker
                v-model="exitFormData.applicationDate"
                type="date"
                placeholder="请选择申请时间"
                style="width: 100%"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="status">
              <el-select
                v-model="exitFormData.status"
                placeholder="请选择状态"
                style="width: 100%"
              >
                <el-option label="申请中" value="applying" />
                <el-option label="评估中" value="evaluating" />
                <el-option label="审批中" value="approving" />
                <el-option label="执行中" value="executing" />
                <el-option label="已完成" value="completed" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="handleExitDialogClose">取消</el-button>
          <el-button type="primary" @click="handleExitSubmit" :loading="exitSubmitLoading">
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'

export default {
  name: 'SupplierDevelopment',
  setup() {
    const router = useRouter()
    const loading = ref(false)
    const activeTab = ref('admission')

    // 弹窗相关
    const admissionFormRef = ref(null)
    const secondaryFormRef = ref(null)
    const exitFormRef = ref(null)

    const admissionDialogVisible = ref(false)
    const secondaryDialogVisible = ref(false)
    const exitDialogVisible = ref(false)

    const isAdmissionEdit = ref(false)
    const isSecondaryEdit = ref(false)
    const isExitEdit = ref(false)

    const admissionDialogTitle = ref('')
    const secondaryDialogTitle = ref('')
    const exitDialogTitle = ref('')

    const admissionSubmitLoading = ref(false)
    const secondarySubmitLoading = ref(false)
    const exitSubmitLoading = ref(false)

    // 搜索表单
    const searchForm = reactive({
      keyword: '',
      status: ''
    })

    const secondarySearchForm = reactive({
      keyword: '',
      status: ''
    })

    const exitSearchForm = reactive({
      keyword: '',
      status: ''
    })

    // 分页信息
    const pagination = reactive({
      currentPage: 1,
      pageSize: 10,
      total: 0
    })

    // 新供应商准入数据
    const admissionData = ref([])
    
    // 二供开发数据
    const secondaryData = ref([])
    
    // 供应商退出数据
    const exitData = ref([])

    // 表单数据
    const admissionFormData = reactive({
      applicationNo: '',
      supplierName: '',
      contactPerson: '',
      phone: '',
      applicationDate: '',
      status: 'pending',
      progress: 20
    })

    const secondaryFormData = reactive({
      projectNo: '',
      materialCode: '',
      materialName: '',
      targetSupplier: '',
      reason: '',
      startDate: '',
      status: 'planning',
      progress: 30
    })

    const exitFormData = reactive({
      exitNo: '',
      supplierName: '',
      reason: '',
      applicant: '',
      applicationDate: '',
      status: 'applying'
    })

    // 表单验证规则
    const admissionFormRules = {
      applicationNo: [
        { required: true, message: '请输入申请编号', trigger: 'blur' }
      ],
      supplierName: [
        { required: true, message: '请输入供应商名称', trigger: 'blur' }
      ],
      contactPerson: [
        { required: true, message: '请输入联系人', trigger: 'blur' }
      ],
      phone: [
        { required: true, message: '请输入联系电话', trigger: 'blur' }
      ],
      applicationDate: [
        { required: true, message: '请选择申请时间', trigger: 'change' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }

    const secondaryFormRules = {
      projectNo: [
        { required: true, message: '请输入项目编号', trigger: 'blur' }
      ],
      materialCode: [
        { required: true, message: '请输入物料编号', trigger: 'blur' }
      ],
      materialName: [
        { required: true, message: '请输入物料名称', trigger: 'blur' }
      ],
      targetSupplier: [
        { required: true, message: '请输入目标供应商', trigger: 'blur' }
      ],
      reason: [
        { required: true, message: '请选择开发原因', trigger: 'change' }
      ],
      startDate: [
        { required: true, message: '请选择开始时间', trigger: 'change' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }

    const exitFormRules = {
      exitNo: [
        { required: true, message: '请输入退出编号', trigger: 'blur' }
      ],
      supplierName: [
        { required: true, message: '请输入供应商名称', trigger: 'blur' }
      ],
      reason: [
        { required: true, message: '请选择退出原因', trigger: 'change' }
      ],
      applicant: [
        { required: true, message: '请输入申请人', trigger: 'blur' }
      ],
      applicationDate: [
        { required: true, message: '请选择申请时间', trigger: 'change' }
      ],
      status: [
        { required: true, message: '请选择状态', trigger: 'change' }
      ]
    }

    // 初始化数据
    const initAdmissionData = () => {
      admissionData.value = [
        {
          id: 1,
          applicationNo: 'APP2024001',
          supplierName: '上海精密制造有限公司',
          contactPerson: '王经理',
          phone: '021-12345678',
          applicationDate: '2024-01-15',
          status: 'pending',
          progress: 20
        },
        {
          id: 2,
          applicationNo: 'APP2024002',
          supplierName: '深圳智能科技股份有限公司',
          contactPerson: '李总监',
          phone: '0755-87654321',
          applicationDate: '2024-01-20',
          status: 'reviewing',
          progress: 45
        },
        {
          id: 3,
          applicationNo: 'APP2024003',
          supplierName: '北京新材料技术有限公司',
          contactPerson: '张工程师',
          phone: '010-11223344',
          applicationDate: '2024-02-01',
          status: 'auditing',
          progress: 70
        },
        {
          id: 4,
          applicationNo: 'APP2024004',
          supplierName: '广州汽车零部件制造厂',
          contactPerson: '陈主管',
          phone: '020-55667788',
          applicationDate: '2024-02-10',
          status: 'testing',
          progress: 85
        },
        {
          id: 5,
          applicationNo: 'APP2024005',
          supplierName: '天津精工机械有限公司',
          contactPerson: '刘经理',
          phone: '022-99887766',
          applicationDate: '2024-02-15',
          status: 'approved',
          progress: 100
        }
      ]
      pagination.total = admissionData.value.length
    }

    const initSecondaryData = () => {
      secondaryData.value = [
        {
          id: 1,
          projectNo: 'SEC2024001',
          materialCode: 'MAT001',
          materialName: '制动盘总成',
          targetSupplier: '博世汽车部件(苏州)有限公司',
          reason: '单一来源风险',
          startDate: '2024-01-10',
          status: 'developing',
          progress: 60
        },
        {
          id: 2,
          projectNo: 'SEC2024002',
          materialCode: 'MAT002',
          materialName: '发动机控制模块',
          targetSupplier: '大陆汽车系统(常熟)有限公司',
          reason: '成本优化',
          startDate: '2024-01-25',
          status: 'validating',
          progress: 80
        },
        {
          id: 3,
          projectNo: 'SEC2024003',
          materialCode: 'MAT003',
          materialName: '空调压缩机',
          targetSupplier: '法雷奥汽车空调湖北有限公司',
          reason: '产能瓶颈',
          startDate: '2024-02-05',
          status: 'planning',
          progress: 30
        }
      ]
    }

    const initExitData = () => {
      exitData.value = [
        {
          id: 1,
          exitNo: 'EXIT2024001',
          supplierName: '某问题供应商A',
          reason: '质量问题严重',
          applicant: '质量部',
          applicationDate: '2024-01-30',
          status: 'evaluating'
        },
        {
          id: 2,
          exitNo: 'EXIT2024002',
          supplierName: '某问题供应商B',
          reason: '交付延迟频繁',
          applicant: '采购部',
          applicationDate: '2024-02-12',
          status: 'approving'
        }
      ]
    }

    // 状态相关方法
    const getStatusType = (status) => {
      const types = {
        pending: 'info',
        reviewing: 'warning',
        auditing: 'primary',
        testing: 'success',
        approved: 'success',
        rejected: 'danger'
      }
      return types[status] || ''
    }

    const getStatusText = (status) => {
      const texts = {
        pending: '待审核',
        reviewing: '审核中',
        auditing: '现场审核',
        testing: '试生产',
        approved: '已通过',
        rejected: '已拒绝'
      }
      return texts[status] || status
    }

    const getSecondaryStatusType = (status) => {
      const types = {
        planning: 'info',
        developing: 'warning',
        validating: 'primary',
        completed: 'success',
        suspended: 'danger'
      }
      return types[status] || ''
    }

    const getSecondaryStatusText = (status) => {
      const texts = {
        planning: '计划中',
        developing: '开发中',
        validating: '验证中',
        completed: '已完成',
        suspended: '已暂停'
      }
      return texts[status] || status
    }

    const getExitStatusType = (status) => {
      const types = {
        applying: 'info',
        evaluating: 'warning',
        approving: 'primary',
        executing: 'danger',
        completed: 'success'
      }
      return types[status] || ''
    }

    const getExitStatusText = (status) => {
      const texts = {
        applying: '申请中',
        evaluating: '评估中',
        approving: '审批中',
        executing: '执行中',
        completed: '已完成'
      }
      return texts[status] || status
    }

    // 事件处理方法
    const handleTabChange = (tabName) => {
      activeTab.value = tabName
    }

    const handleNewApplication = () => {
      isAdmissionEdit.value = false
      admissionDialogTitle.value = '新增准入申请'
      resetAdmissionForm()
      admissionDialogVisible.value = true
    }

    const handleSearch = () => {
      console.log('搜索准入申请', searchForm)
    }

    const handleViewApplication = (row) => {
      router.push(`/supplier-development/application/${row.id}`)
    }

    const handleAudit = (row) => {
      router.push(`/supplier-development/audit/${row.id}`)
    }

    const handleTestProduction = (row) => {
      router.push(`/supplier-development/test-production/${row.id}`)
    }

    const handleCommand = (command) => {
      if (command.action === 'edit') {
        handleEditApplication(command.row)
      } else {
        console.log('执行操作', command)
      }
    }

    const handleEditApplication = (row) => {
      isAdmissionEdit.value = true
      admissionDialogTitle.value = '编辑准入申请'
      Object.keys(admissionFormData).forEach(key => {
        if (row[key] !== undefined) {
          admissionFormData[key] = row[key]
        }
      })
      admissionDialogVisible.value = true
    }

    const handleNewSecondary = () => {
      isSecondaryEdit.value = false
      secondaryDialogTitle.value = '新增二供开发'
      resetSecondaryForm()
      secondaryDialogVisible.value = true
    }

    const handleSecondarySearch = () => {
      console.log('搜索二供开发', secondarySearchForm)
    }

    const handleViewSecondary = (row) => {
      router.push(`/supplier-development/secondary-detail/${row.id}`)
    }

    const handleEditSecondary = (row) => {
      isSecondaryEdit.value = true
      secondaryDialogTitle.value = '编辑二供开发'
      Object.keys(secondaryFormData).forEach(key => {
        if (row[key] !== undefined) {
          secondaryFormData[key] = row[key]
        }
      })
      secondaryDialogVisible.value = true
    }

    const handleSecondaryAudit = (row) => {
      console.log('二供审核', row)
    }

    const handleNewExit = () => {
      isExitEdit.value = false
      exitDialogTitle.value = '新增退出申请'
      resetExitForm()
      exitDialogVisible.value = true
    }

    const handleExitSearch = () => {
      console.log('搜索退出申请', exitSearchForm)
    }

    const handleViewExit = (row) => {
      router.push(`/supplier-development/exit-detail/${row.id}`)
    }

    const handleEditExit = (row) => {
      isExitEdit.value = true
      exitDialogTitle.value = '编辑退出申请'
      Object.keys(exitFormData).forEach(key => {
        if (row[key] !== undefined) {
          exitFormData[key] = row[key]
        }
      })
      exitDialogVisible.value = true
    }

    const handleExitApprove = (row) => {
      console.log('退出审批', row)
    }

    const handleSizeChange = (val) => {
      pagination.pageSize = val
    }

    const handleCurrentChange = (val) => {
      pagination.currentPage = val
    }

    // 重置表单方法
    const resetAdmissionForm = () => {
      Object.assign(admissionFormData, {
        applicationNo: '',
        supplierName: '',
        contactPerson: '',
        phone: '',
        applicationDate: '',
        status: 'pending',
        progress: 20
      })
      if (admissionFormRef.value) {
        admissionFormRef.value.clearValidate()
      }
    }

    const resetSecondaryForm = () => {
      Object.assign(secondaryFormData, {
        projectNo: '',
        materialCode: '',
        materialName: '',
        targetSupplier: '',
        reason: '',
        startDate: '',
        status: 'planning',
        progress: 30
      })
      if (secondaryFormRef.value) {
        secondaryFormRef.value.clearValidate()
      }
    }

    const resetExitForm = () => {
      Object.assign(exitFormData, {
        exitNo: '',
        supplierName: '',
        reason: '',
        applicant: '',
        applicationDate: '',
        status: 'applying'
      })
      if (exitFormRef.value) {
        exitFormRef.value.clearValidate()
      }
    }

    // 弹窗关闭方法
    const handleAdmissionDialogClose = () => {
      admissionDialogVisible.value = false
      resetAdmissionForm()
    }

    const handleSecondaryDialogClose = () => {
      secondaryDialogVisible.value = false
      resetSecondaryForm()
    }

    const handleExitDialogClose = () => {
      exitDialogVisible.value = false
      resetExitForm()
    }

    // 表单提交方法
    const handleAdmissionSubmit = async () => {
      if (!admissionFormRef.value) return

      try {
        await admissionFormRef.value.validate()
        admissionSubmitLoading.value = true

        await new Promise(resolve => setTimeout(resolve, 1000))

        if (isAdmissionEdit.value) {
          const index = admissionData.value.findIndex(item => item.applicationNo === admissionFormData.applicationNo)
          if (index !== -1) {
            Object.assign(admissionData.value[index], admissionFormData)
          }
          ElMessage.success('编辑成功')
        } else {
          const newId = Math.max(...admissionData.value.map(item => item.id)) + 1
          admissionData.value.unshift({
            id: newId,
            ...admissionFormData
          })
          ElMessage.success('新增成功')
        }

        handleAdmissionDialogClose()
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        admissionSubmitLoading.value = false
      }
    }

    const handleSecondarySubmit = async () => {
      if (!secondaryFormRef.value) return

      try {
        await secondaryFormRef.value.validate()
        secondarySubmitLoading.value = true

        await new Promise(resolve => setTimeout(resolve, 1000))

        if (isSecondaryEdit.value) {
          const index = secondaryData.value.findIndex(item => item.projectNo === secondaryFormData.projectNo)
          if (index !== -1) {
            Object.assign(secondaryData.value[index], secondaryFormData)
          }
          ElMessage.success('编辑成功')
        } else {
          const newId = Math.max(...secondaryData.value.map(item => item.id)) + 1
          secondaryData.value.unshift({
            id: newId,
            ...secondaryFormData
          })
          ElMessage.success('新增成功')
        }

        handleSecondaryDialogClose()
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        secondarySubmitLoading.value = false
      }
    }

    const handleExitSubmit = async () => {
      if (!exitFormRef.value) return

      try {
        await exitFormRef.value.validate()
        exitSubmitLoading.value = true

        await new Promise(resolve => setTimeout(resolve, 1000))

        if (isExitEdit.value) {
          const index = exitData.value.findIndex(item => item.exitNo === exitFormData.exitNo)
          if (index !== -1) {
            Object.assign(exitData.value[index], exitFormData)
          }
          ElMessage.success('编辑成功')
        } else {
          const newId = Math.max(...exitData.value.map(item => item.id)) + 1
          exitData.value.unshift({
            id: newId,
            ...exitFormData
          })
          ElMessage.success('新增成功')
        }

        handleExitDialogClose()
      } catch (error) {
        console.error('表单验证失败:', error)
      } finally {
        exitSubmitLoading.value = false
      }
    }

    onMounted(() => {
      initAdmissionData()
      initSecondaryData()
      initExitData()
    })

    return {
      loading,
      activeTab,
      searchForm,
      secondarySearchForm,
      exitSearchForm,
      pagination,
      admissionData,
      secondaryData,
      exitData,
      // 弹窗相关
      admissionFormRef,
      secondaryFormRef,
      exitFormRef,
      admissionDialogVisible,
      secondaryDialogVisible,
      exitDialogVisible,
      isAdmissionEdit,
      isSecondaryEdit,
      isExitEdit,
      admissionDialogTitle,
      secondaryDialogTitle,
      exitDialogTitle,
      admissionSubmitLoading,
      secondarySubmitLoading,
      exitSubmitLoading,
      admissionFormData,
      secondaryFormData,
      exitFormData,
      admissionFormRules,
      secondaryFormRules,
      exitFormRules,
      // 状态方法
      getStatusType,
      getStatusText,
      getSecondaryStatusType,
      getSecondaryStatusText,
      getExitStatusType,
      getExitStatusText,
      // 事件处理方法
      handleTabChange,
      handleNewApplication,
      handleSearch,
      handleViewApplication,
      handleAudit,
      handleTestProduction,
      handleCommand,
      handleEditApplication,
      handleNewSecondary,
      handleSecondarySearch,
      handleViewSecondary,
      handleEditSecondary,
      handleSecondaryAudit,
      handleNewExit,
      handleExitSearch,
      handleViewExit,
      handleEditExit,
      handleExitApprove,
      handleSizeChange,
      handleCurrentChange,
      // 弹窗方法
      resetAdmissionForm,
      resetSecondaryForm,
      resetExitForm,
      handleAdmissionDialogClose,
      handleSecondaryDialogClose,
      handleExitDialogClose,
      handleAdmissionSubmit,
      handleSecondarySubmit,
      handleExitSubmit
    }
  }
}
</script>

<style scoped>
.supplier-development-container {
  padding: 0;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  color: #303133;
  margin-bottom: 8px;
}

.page-header p {
  color: #909399;
  font-size: 14px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  position: relative;
  overflow: hidden;
}

.stats-content {
  padding: 10px 0;
}

.stats-number {
  font-size: 28px;
  font-weight: bold;
  color: #303133;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #909399;
  margin-top: 8px;
}

.stats-icon {
  position: absolute;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32px;
  opacity: 0.3;
}

.stats-icon.pending {
  color: #909399;
}

.stats-icon.auditing {
  color: #409EFF;
}

.stats-icon.testing {
  color: #67C23A;
}

.stats-icon.developing {
  color: #E6A23C;
}

.main-card {
  margin-bottom: 20px;
}

.tab-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.search-area {
  display: flex;
  align-items: center;
}

.action-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  align-items: center;
}

.action-buttons .el-button {
  margin: 0;
  min-width: auto;
  padding: 5px 8px;
  font-size: 12px;
}

.action-buttons .el-dropdown {
  margin-left: 4px;
}

.pagination-container {
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .action-buttons .el-button {
    padding: 4px 6px;
    font-size: 11px;
  }

  .stats-row .el-col {
    margin-bottom: 10px;
  }
}

@media (max-width: 768px) {
  .tab-header {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }

  .search-area {
    flex-direction: column;
    gap: 10px;
  }

  .search-area .el-input,
  .search-area .el-select {
    width: 100% !important;
  }
}
</style>
